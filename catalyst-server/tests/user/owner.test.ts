import { Connection, EntityManager, IDatabaseDriver } from '@mikro-orm/core';
import Application from 'application';
import { getToken } from 'core/auth/authUtils';
import { expect } from 'chai';
import { ID_PREFIX_0, restoreDbFromLocalBackup } from 'loadFixtures';
import supertest, { SuperTest, Test } from 'supertest';
import { Id } from 'core/utils/Id';
import { PayloadKeys } from 'core/auth/JwtPayload';
import { RoleNames, roleMap } from 'core/contracts/enums/RoleNames';

let request: SuperTest<Test>;
let application: Application;
let em: EntityManager<IDatabaseDriver<Connection>> | undefined = undefined;
let curatorToken: string;

describe(')))))))))))))) Owner Tests ((((((((((((((', async () => {
  before(async () => {
    application = new Application();
    await application.init();
    await application.start();
    em = application.orm?.em.fork();
    request = supertest(application.app);
    // user <EMAIL>
    // this is the prefix for users for monumentsMen (see loadFixtures.ts)
    curatorToken = getToken({
      [PayloadKeys.USER_KEY]: Id.simpleId(3, ID_PREFIX_0),
      [PayloadKeys.TENANT_KEY]: Id.simpleId(1),
      [PayloadKeys.ROLES_KEY]: [roleMap[RoleNames.CURATOR]],
    }).token;
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
  });

  after(async () => {
    application.server?.close();
  });

  it('search owners by emailAddress', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
          queryOwners(
            searchSortInput: {
              searchFields: [
                {
                  fieldNames: ["user.emailAddress"]
                  operator: MATCH
                  searchValue: "<EMAIL>"
                }
              ]
            }
          ) {      
            results {  
              id createdAt updatedAt organizationRole
              user {
                emailAddress firstName lastName org1 org2 org3 org4 phone altContact
              }
            }
          }
        }`,
      })
      .expect(200);
    const { results } = response.body.data.queryOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(1);
    expect(results[0].user.emailAddress).to.equal('<EMAIL>')
  });

  it('filter owner by email using OR', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOwners(
          searchSortInput: {
            searchFields: [{ fieldNames: ["user.emailAddress"], operator: IN, searchValue: ["<EMAIL>", "<EMAIL>"] }]
          }
        ) {
          results {  
              id createdAt updatedAt organizationRole
              user {
                emailAddress firstName lastName org1 org2 org3 org4 phone altContact
              }
            }
        }
      }
      `,
      })
      .expect(200);
    const { results } = response.body.data.queryOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(2);
  });

  // Note: enum input values have to be treated as strings when usinjg searchFieldGroups because of JSONType
  // category_1* should match 4 opps (category_1 / category_10) and catery_2* should match 2 ops but 1 of those is pending so ultimately only 1
  // this is a total of 5 opps
  it('filter owners using OR and AND by using both searchFields and jsonSearchGroups', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOwners(
          searchSortInput: {
            searchFields: [{ fieldNames: ["organizationRole"], operator: MATCH, searchValue: ["my random role"] }],
            jsonSearchGroups: [
              { operator: "or", operands: [
                { fieldNames: ["user.emailAddress"], operator: "~", searchValue: "curator" },
                { fieldNames: ["user.emailAddress"], operator: "~", searchValue: "admin" }
              ] }
            ]
            sortFields: [{ fieldName: "user.emailAddress", ascending: true }]
          }
        ) {
          results {  
              id createdAt updatedAt organizationRole
              user {
                emailAddress firstName lastName org1 org2 org3 org4 phone altContact
              }
            }
        }
      }
      `,
      })
      .expect(200);
    const { results } = response.body.data.queryOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(1);
    expect(results[0].user.emailAddress).to.equal('<EMAIL>');
  });

  it('page owners and sort', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOwners(
          pagingInput: { pageSize: 5, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "id", ascending: false }, { fieldName: "user.firstName", ascending: true}]
          }
        ) {
          results {  
              id createdAt updatedAt organizationRole
              user {
                emailAddress firstName lastName org1 org2 org3 org4 phone altContact
              }
            }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    const { results, pageInfo } = response.body.data.queryOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(3);
    expect(pageInfo.hasNext).equal(false);
    expect(pageInfo.hasPrevious).equal(false);
    expect(pageInfo.lastCursor).equal('0');
    expect(pageInfo.lastPageSize).equal(5);
    expect(pageInfo.retrievedCount).equal(5);
    expect(pageInfo.totalCount).equal(5);
  });

  it('page owners and sort by org', async () => {
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `query {
        queryOwners(
          pagingInput: { pageSize: 5, cursor: "0" },
          searchSortInput: {
            sortFields: [{ fieldName: "user.lastName", ascending: false }, { fieldName: "user.firstName", ascending: false }]
          }
        ) {
          results {  
              id createdAt updatedAt organizationRole
              user {
                emailAddress firstName lastName org1 org2 org3 org4 phone altContact
              }
            }
          pageInfo {
            hasNext hasPrevious lastCursor lastPageSize retrievedCount totalCount
          }
        }
      }
      `,
      })
      .expect(200);
    const { results, pageInfo } = response.body.data.queryOwners;
    expect(results).to.be.a('array');
    expect(results.length).equal(5);
    expect(pageInfo.hasNext).equal(false);
    expect(pageInfo.hasPrevious).equal(false);
    expect(pageInfo.lastCursor).equal('0');
    expect(pageInfo.lastPageSize).equal(5);
    expect(pageInfo.retrievedCount).equal(5);
    expect(pageInfo.totalCount).equal(5);
  });

  it('should create owner for another user', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
          createOwner(
            input: {
              emailAddress: "<EMAIL>"
              firstName: "Johnny"
              lastName: "Smith"
              org1: "Something"
              org2: "NA"
              org3: "DA"
              org4: "TA"
              phone: "3242342342"
              altContact: "<EMAIL>"
              organizationRole: "Tada"
              opportunityId: "00000000-0000-0000-0000-000000000001"
            }
          ) {
            id
            organizationRole
            user {
              emailAddress
              firstName
              lastName
            }
          }
        }
        `,
      })
      .expect(200);

    const { createOwner } = response.body.data;
    expect(createOwner).to.be.a('object');
    expect(createOwner.id).to.not.be.null;
    expect(createOwner.user.emailAddress).to.not.be.null;
  });

  it('should update owner status and set dates', async () => {
    restoreDbFromLocalBackup();
    console.log('🚀 Database cleared, fixtures loaded');
    const response = await request
      .post('/curator')
      .set('Authorization', `Bearer ${curatorToken}`)
      .send({
        query: `mutation {
        updateOwner(
          id: "00000000-0000-0000-0000-000000000001"
          input: { organizationRole: "Updated my role" }
          links: {}
        ) {
          id
          organizationRole
          user {
            emailAddress
            firstName
            lastName
          }
        }
      }
      `,
      })
      .expect(200);

    const { updateOwner } = response.body.data;
    expect(updateOwner).to.be.a('object');
    expect(updateOwner.id).to.not.be.null;
    expect(updateOwner.user.emailAddress).to.not.be.null;
    expect(updateOwner.organizationRole).to.equal('Updated my role');
    
  });
});